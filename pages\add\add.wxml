<!--pages/add/add.wxml-->
<view class="container">
  <view class="form-card card">
    <view class="form-title">记一笔支出</view>
    
    <!-- 名称输入 -->
    <view class="input-group">
      <view class="input-label">支出名称 *</view>
      <input 
        class="input-field" 
        placeholder="请输入支出名称" 
        value="{{name}}"
        bindinput="onNameInput"
        maxlength="20"
      />
    </view>

    <!-- 类型选择 -->
    <view class="input-group">
      <view class="input-label">支出类型 *</view>
      <view class="type-selector">
        <view 
          wx:for="{{expenseTypes}}" 
          wx:key="id"
          class="type-item {{selectedTypeId === item.id ? 'active' : ''}}"
          bindtap="selectType"
          data-id="{{item.id}}"
          style="border-color: {{selectedTypeId === item.id ? item.color : 'transparent'}}"
        >
          <view class="type-icon">{{item.icon}}</view>
          <view class="type-name" style="color: {{selectedTypeId === item.id ? item.color : '#666'}}">
            {{item.name}}
          </view>
        </view>
      </view>
    </view>

    <!-- 金额输入 -->
    <view class="input-group">
      <view class="input-label">支出金额 *</view>
      <view class="amount-input-wrapper">
        <text class="currency">¥</text>
        <input 
          class="amount-input" 
          placeholder="0.00" 
          value="{{amount}}"
          bindinput="onAmountInput"
          type="digit"
        />
      </view>
      
      <!-- 快速金额选择 -->
      <view class="quick-amounts">
        <button 
          wx:for="{{['10', '20', '50', '100', '200', '500']}}" 
          wx:key="*this"
          class="quick-amount-btn"
          bindtap="quickAmount"
          data-amount="{{item}}"
        >
          {{item}}
        </button>
      </view>
    </view>

    <!-- 日期选择 -->
    <view class="input-group">
      <view class="input-label">支出日期 *</view>
      <picker 
        mode="date" 
        value="{{date}}" 
        bindchange="onDateChange"
        class="date-picker"
      >
        <view class="picker-display">
          <text class="date-text">{{date}}</text>
          <text class="picker-arrow">📅</text>
        </view>
      </picker>
    </view>

    <!-- 备注输入 -->
    <view class="input-group">
      <view class="input-label">备注（可选）</view>
      <textarea 
        class="textarea-field" 
        placeholder="添加备注信息..." 
        value="{{note}}"
        bindinput="onNoteInput"
        maxlength="100"
        show-confirm-bar="{{false}}"
      />
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="btn-primary submit-btn" bindtap="submitRecord">
        确认记录
      </button>
      <button class="btn-secondary reset-btn" bindtap="resetForm">
        重置
      </button>
    </view>
  </view>
</view>

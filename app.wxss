/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx 0;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 按钮样式 */
.btn-primary {
  background: #1976D2;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.3);
}

.btn-secondary {
  background: white;
  color: #1976D2;
  border: 2rpx solid #1976D2;
  border-radius: 12rpx;
  padding: 22rpx 46rpx;
  font-size: 32rpx;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 32rpx;
  color: #333;
}

.input-field:focus {
  border-color: #1976D2;
  background: white;
}

/* 类型选择器样式 */
.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  min-width: 120rpx;
}

.type-item.active {
  background: #e3f2fd;
  border-color: #1976D2;
}

.type-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.type-name {
  font-size: 24rpx;
  color: #666;
}

.type-item.active .type-name {
  color: #1976D2;
  font-weight: 500;
}

/* 统计卡片 */
.stat-card {
  background: #1976D2;
  color: white;
  text-align: center;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(25, 118, 210, 0.2);
}

.stat-amount {
  font-size: 72rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.stat-label {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 记录列表 */
.record-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 80rpx;
  text-align: center;
}

.record-info {
  flex: 1;
}

.record-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-note {
  font-size: 24rpx;
  color: #999;
}

.record-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4757;
}

.record-date {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

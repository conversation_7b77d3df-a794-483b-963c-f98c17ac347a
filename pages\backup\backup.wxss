/* pages/backup/backup.wxss */
.container {
  padding: 20rpx 24rpx;
  min-height: 100vh;
  background: #f5f5f5;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.help-btn {
  background: #1976D2;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 数据概览 */
.data-overview {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.overview-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 8rpx solid #1976D2;
}

.overview-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 80rpx;
  text-align: center;
}

.overview-info {
  flex: 1;
}

.overview-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.overview-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 描述文本 */
.backup-description,
.restore-description {
  margin-bottom: 30rpx;
}

.desc-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.export-btn,
.import-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.btn-icon {
  font-size: 40rpx;
}

/* 提示信息 */
.export-tips,
.import-tips {
  background: #e3f2fd;
  border-radius: 12rpx;
  padding: 24rpx;
}

.tip-item {
  display: block;
  font-size: 24rpx;
  color: #1976D2;
  line-height: 1.8;
  margin-bottom: 8rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 数据管理 */
.management-section {
  margin-bottom: 24rpx;
}

.management-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #fff5f5;
  border-radius: 12rpx;
  border: 2rpx solid #ffebee;
}

.management-info {
  flex: 1;
}

.management-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.management-desc {
  font-size: 24rpx;
  color: #999;
}

.btn-danger {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
}

/* 警告框 */
.warning-box {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
}

.warning-icon {
  font-size: 32rpx;
}

.warning-text {
  font-size: 26rpx;
  color: #856404;
  font-weight: 500;
}

/* 备份建议 */
.suggestions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.suggestion-icon {
  font-size: 36rpx;
  width: 60rpx;
  text-align: center;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

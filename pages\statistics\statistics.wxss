/* pages/statistics/statistics.wxss */
.container {
  padding: 20rpx 24rpx;
  min-height: 100vh;
  background: #f5f5f5;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 下拉选择器 */
.period-picker,
.type-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  min-height: 88rpx;
  box-sizing: border-box;
}

.picker-display:active {
  background: #e9ecef;
}

.picker-text {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}

.picker-arrow {
  font-size: 24rpx;
  color: #1976D2;
  margin-left: 16rpx;
}

/* 统计概览 */
.stats-overview {
  display: flex;
  gap: 12rpx;
  margin-bottom: 25rpx;
  width: 100%;
}

.stat-card {
  flex: 1;
  background: #1976D2;
  color: white;
  text-align: center;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(25, 118, 210, 0.2);
}

.stat-amount {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 图表切换 */
.chart-switcher {
  display: flex;
  gap: 12rpx;
  margin-bottom: 30rpx;
  width: 100%;
}

.chart-btn {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 24rpx;
  flex: 1;
}

.chart-btn.active {
  background: #1976D2;
  color: white;
  border-color: #1976D2;
}

/* 图表容器 */
.chart-container {
  min-height: 400rpx;
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.empty-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

.chart-content {
  min-height: 300rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1976D2;
  text-align: center;
  margin-bottom: 24rpx;
}

/* 折线图样式 */
.line-chart-display {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 240rpx;
  padding: 20rpx;
  background: #f8fbff;
  border-radius: 12rpx;
  border: 2rpx solid #e3f2fd;
}

.line-point {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 80rpx;
}

.point-date {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.point-bar {
  width: 24rpx;
  min-height: 20rpx;
  border-radius: 12rpx 12rpx 0 0;
  margin-bottom: 8rpx;
}

.point-amount {
  font-size: 18rpx;
  color: #1976D2;
  font-weight: bold;
}

/* 柱状图样式 */
.type-bars {
  space-y: 24rpx;
}

.bar-item {
  margin-bottom: 24rpx;
}

.bar-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.bar-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.bar-visual {
  height: 24rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 12rpx;
  transition: width 0.3s ease;
}

.bar-amount {
  font-size: 24rpx;
  color: #666;
  text-align: right;
}

/* 饼图样式 */
.pie-legend {
  space-y: 20rpx;
}

.legend-item {
  margin-bottom: 20rpx;
}

.legend-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.legend-color {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
}

.legend-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.legend-percent {
  font-size: 24rpx;
  color: #666;
  margin-left: 16rpx;
}

.legend-amount {
  font-size: 28rpx;
  color: #1976D2;
  font-weight: bold;
}

/* 记录预览 */
.view-all-btn {
  background: #1976D2;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.records-preview {
  max-height: 600rpx;
  overflow-y: auto;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.record-info {
  flex: 1;
}

.record-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-meta {
  font-size: 24rpx;
  color: #999;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4757;
}

// pages/statistics/statistics.js
const storageManager = require('../../utils/storage.js')

Page({
  data: {
    selectedPeriod: '7days',
    selectedPeriodIndex: 0,
    selectedTypeId: null,
    selectedTypeIndex: 0,
    periods: [
      { key: '7days', name: '最近7天' },
      { key: '14days', name: '最近14天' },
      { key: '30days', name: '最近30天' },
      { key: 'thisMonth', name: '本月' },
      { key: '3months', name: '近三月' }
    ],
    expenseTypes: [],
    typeOptions: [],
    statistics: {
      totalAmount: 0,
      recordCount: 0,
      typeStats: {},
      dateStats: {},
      records: [],
      formattedTotalAmount: '0.00'
    },
    processedTypeStats: {},
    averageAmount: '0.00',
    chartType: 'line', // line, bar, pie
    chartData: null
  },

  onLoad() {
    const expenseTypes = getApp().globalData.expenseTypes
    // 构建类型选项，包含"全部"选项
    const typeOptions = [
      { id: null, name: '全部类型', icon: '📊' },
      ...expenseTypes.map(type => ({
        id: type.id,
        name: `${type.icon} ${type.name}`,
        icon: type.icon
      }))
    ]

    this.setData({
      expenseTypes: expenseTypes,
      typeOptions: typeOptions
    })
    this.loadStatistics()
  },

  onShow() {
    this.loadStatistics()
  },

  // 时间段下拉框变化
  onPeriodChange(e) {
    const index = e.detail.value
    const selectedPeriod = this.data.periods[index].key
    this.setData({
      selectedPeriodIndex: index,
      selectedPeriod: selectedPeriod
    })
    this.loadStatistics()
  },

  // 类型下拉框变化
  onTypeChange(e) {
    const index = e.detail.value
    const selectedTypeId = this.data.typeOptions[index].id
    this.setData({
      selectedTypeIndex: index,
      selectedTypeId: selectedTypeId
    })
    this.loadStatistics()
  },

  // 切换图表类型
  switchChart(e) {
    const chartType = e.currentTarget.dataset.type
    this.setData({
      chartType: chartType
    })
    this.generateChartData()
  },

  // 加载统计数据
  loadStatistics() {
    const { selectedPeriod, selectedTypeId } = this.data
    const dateRange = storageManager.getDateRange(selectedPeriod)
    const statistics = storageManager.getStatistics(dateRange.start, dateRange.end, selectedTypeId)
    
    // 为记录添加类型信息
    const recordsWithType = statistics.records.map(record => {
      const type = this.data.expenseTypes.find(t => t.id === record.typeId)
      return {
        ...record,
        typeName: type ? type.name : '未知',
        typeIcon: type ? type.icon : '💰',
        typeColor: type ? type.color : '#999'
      }
    })

    // 计算平均支出
    const averageAmount = statistics.recordCount > 0
      ? (statistics.totalAmount / statistics.recordCount).toFixed(2)
      : '0.00'

    // 预计算类型统计数据，避免在WXML中使用复杂表达式
    const processedTypeStats = {}
    Object.keys(statistics.typeStats).forEach(typeId => {
      const typeData = statistics.typeStats[typeId]
      const percentage = statistics.totalAmount > 0
        ? (typeData.amount / statistics.totalAmount * 100).toFixed(1)
        : '0.0'

      processedTypeStats[typeId] = {
        ...typeData,
        percentage: percentage,
        formattedAmount: typeData.amount.toFixed(2)
      }
    })

    this.setData({
      statistics: {
        ...statistics,
        records: recordsWithType,
        formattedTotalAmount: statistics.totalAmount.toFixed(2)
      },
      processedTypeStats: processedTypeStats,
      averageAmount: averageAmount
    })

    this.generateChartData()
  },

  // 生成图表数据
  generateChartData() {
    const { statistics, chartType, expenseTypes } = this.data
    let chartData = null

    switch (chartType) {
      case 'line':
        chartData = this.generateLineChartData()
        break
      case 'bar':
        chartData = this.generateBarChartData()
        break
      case 'pie':
        chartData = this.generatePieChartData()
        break
    }

    this.setData({
      chartData: chartData
    })
  },

  // 生成折线图数据
  generateLineChartData() {
    const { statistics } = this.data
    const dateStats = statistics.dateStats
    
    // 按日期排序
    const sortedDates = Object.keys(dateStats).sort()
    const labels = sortedDates.map(date => {
      const d = new Date(date)
      return `${d.getMonth() + 1}/${d.getDate()}`
    })
    const data = sortedDates.map(date => dateStats[date].amount)

    return {
      type: 'line',
      labels: labels,
      datasets: [{
        label: '每日支出',
        data: data,
        borderColor: '#4A90E2',
        backgroundColor: 'rgba(74, 144, 226, 0.1)',
        tension: 0.4
      }]
    }
  },

  // 生成柱状图数据
  generateBarChartData() {
    const { statistics, expenseTypes } = this.data
    const typeStats = statistics.typeStats
    
    const labels = []
    const data = []
    const colors = []

    Object.keys(typeStats).forEach(typeId => {
      const type = expenseTypes.find(t => t.id == typeId)
      if (type) {
        labels.push(type.name)
        data.push(typeStats[typeId].amount)
        colors.push(type.color)
      }
    })

    return {
      type: 'bar',
      labels: labels,
      datasets: [{
        label: '各类型支出',
        data: data,
        backgroundColor: colors,
        borderColor: colors,
        borderWidth: 1
      }]
    }
  },

  // 生成饼图数据
  generatePieChartData() {
    const { statistics, expenseTypes } = this.data
    const typeStats = statistics.typeStats
    
    const labels = []
    const data = []
    const colors = []

    Object.keys(typeStats).forEach(typeId => {
      const type = expenseTypes.find(t => t.id == typeId)
      if (type) {
        labels.push(type.name)
        data.push(typeStats[typeId].amount)
        colors.push(type.color)
      }
    })

    return {
      type: 'pie',
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: colors,
        borderColor: '#fff',
        borderWidth: 2
      }]
    }
  },

  // 查看详细记录
  viewDetails() {
    const { statistics } = this.data
    if (statistics.records.length === 0) {
      wx.showToast({
        title: '暂无记录',
        icon: 'none'
      })
      return
    }

    // 这里可以跳转到详细记录页面或显示模态框
    wx.showModal({
      title: '统计详情',
      content: `共${statistics.recordCount}条记录，总支出¥${statistics.totalAmount.toFixed(2)}`,
      showCancel: false
    })
  }
})

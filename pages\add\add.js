// pages/add/add.js
const storageManager = require('../../utils/storage.js')

Page({
  data: {
    name: '',
    selectedTypeId: null,
    amount: '',
    date: '',
    note: '',
    expenseTypes: []
  },

  onLoad() {
    const today = storageManager.formatDate(new Date())
    this.setData({
      date: today,
      expenseTypes: getApp().globalData.expenseTypes
    })
  },

  // 输入名称
  onNameInput(e) {
    this.setData({
      name: e.detail.value
    })
  },

  // 选择类型
  selectType(e) {
    const typeId = e.currentTarget.dataset.id
    this.setData({
      selectedTypeId: typeId
    })
  },

  // 输入金额
  onAmountInput(e) {
    let value = e.detail.value
    // 只允许数字和小数点
    value = value.replace(/[^\d.]/g, '')
    
    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
      value = parts[0] + '.' + parts.slice(1).join('')
    }
    
    // 限制小数点后两位
    if (parts[1] && parts[1].length > 2) {
      value = parts[0] + '.' + parts[1].substring(0, 2)
    }
    
    this.setData({
      amount: value
    })
  },

  // 选择日期
  onDateChange(e) {
    this.setData({
      date: e.detail.value
    })
  },

  // 输入备注
  onNoteInput(e) {
    this.setData({
      note: e.detail.value
    })
  },

  // 提交记录
  submitRecord() {
    const { name, selectedTypeId, amount, date, note } = this.data
    
    // 验证必填字段
    if (!name.trim()) {
      wx.showToast({
        title: '请输入名称',
        icon: 'none'
      })
      return
    }
    
    if (!selectedTypeId) {
      wx.showToast({
        title: '请选择类型',
        icon: 'none'
      })
      return
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      wx.showToast({
        title: '请输入正确的金额',
        icon: 'none'
      })
      return
    }
    
    if (!date) {
      wx.showToast({
        title: '请选择日期',
        icon: 'none'
      })
      return
    }

    try {
      const record = {
        name: name.trim(),
        typeId: selectedTypeId,
        amount: amount,
        date: date,
        note: note.trim()
      }
      
      storageManager.addRecord(record)
      
      wx.showToast({
        title: '记录成功',
        icon: 'success'
      })
      
      // 清空表单
      this.resetForm()
      
      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
      
    } catch (error) {
      wx.showToast({
        title: '记录失败',
        icon: 'error'
      })
      console.error('记录失败:', error)
    }
  },

  // 重置表单
  resetForm() {
    const today = storageManager.formatDate(new Date())
    this.setData({
      name: '',
      selectedTypeId: null,
      amount: '',
      date: today,
      note: ''
    })
  },

  // 快速输入金额
  quickAmount(e) {
    const amount = e.currentTarget.dataset.amount
    this.setData({
      amount: amount
    })
  }
})

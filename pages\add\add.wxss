/* pages/add/add.wxss */
.container {
  padding: 20rpx 24rpx;
  min-height: 100vh;
  background: #f5f5f5;
  width: 100%;
  box-sizing: border-box;
}

.form-card {
  margin-bottom: 30rpx;
  padding: 32rpx 24rpx;
  width: 100%;
  box-sizing: border-box;
}

.form-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 28rpx 24rpx;
  font-size: 32rpx;
  color: #333;
  width: 100%;
  min-height: 88rpx;
  line-height: 1.4;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #4A90E2;
  background: white;
}

/* 类型选择器 */
.type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 18rpx 12rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  width: calc(25% - 12rpx);
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.type-item.active {
  background: #e3f2fd;
  transform: scale(1.02);
}

.type-icon {
  font-size: 44rpx;
  margin-bottom: 8rpx;
}

.type-name {
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
}

/* 金额输入 */
.amount-input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
}

.amount-input-wrapper:focus-within {
  border-color: #4A90E2;
  background: white;
}

.currency {
  font-size: 36rpx;
  color: #4A90E2;
  font-weight: bold;
  margin-right: 16rpx;
}

.amount-input {
  flex: 1;
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.quick-amounts {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
}

.quick-amount-btn {
  background: #e3f2fd;
  color: #4A90E2;
  border: 1rpx solid #4A90E2;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  min-width: 80rpx;
}

.quick-amount-btn:active {
  background: #4A90E2;
  color: white;
}

/* 日期选择器 */
.date-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
}

.date-text {
  font-size: 32rpx;
  color: #333;
}

.picker-arrow {
  font-size: 32rpx;
  color: #4A90E2;
}

/* 备注输入 */
.textarea-field {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  width: 100%;
  min-height: 120rpx;
  box-sizing: border-box;
}

.textarea-field:focus {
  border-color: #4A90E2;
  background: white;
}

/* 提交区域 */
.submit-section {
  display: flex;
  gap: 20rpx;
  margin-top: 60rpx;
}

.submit-btn {
  flex: 2;
  padding: 32rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.reset-btn {
  flex: 1;
  padding: 30rpx;
  font-size: 32rpx;
}

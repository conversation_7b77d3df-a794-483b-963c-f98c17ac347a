<!--pages/statistics/statistics.wxml-->
<view class="container">
  <!-- 时间段选择 -->
  <view class="card">
    <view class="section-title">时间段</view>
    <picker
      mode="selector"
      range="{{periods}}"
      range-key="name"
      value="{{selectedPeriodIndex}}"
      bindchange="onPeriodChange"
      class="period-picker"
    >
      <view class="picker-display">
        <text class="picker-text">{{periods[selectedPeriodIndex].name}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>
  </view>

  <!-- 类型筛选 -->
  <view class="card">
    <view class="section-title">类型筛选</view>
    <picker
      mode="selector"
      range="{{typeOptions}}"
      range-key="name"
      value="{{selectedTypeIndex}}"
      bindchange="onTypeChange"
      class="type-picker"
    >
      <view class="picker-display">
        <text class="picker-text">{{typeOptions[selectedTypeIndex].name}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="stat-card">
      <view class="stat-amount">¥{{statistics.formattedTotalAmount}}</view>
      <view class="stat-label">总支出</view>
    </view>
    
    <view class="stat-card" style="background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%);">
      <view class="stat-amount">{{statistics.recordCount}}</view>
      <view class="stat-label">记录数</view>
    </view>
    
    <view class="stat-card" style="background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);">
      <view class="stat-amount">¥{{averageAmount}}</view>
      <view class="stat-label">平均支出</view>
    </view>
  </view>

  <!-- 图表切换 -->
  <view class="card">
    <view class="section-title">数据图表</view>
    <view class="chart-switcher">
      <button 
        class="chart-btn {{chartType === 'line' ? 'active' : ''}}"
        bindtap="switchChart"
        data-type="line"
      >
        📈 折线图
      </button>
      <button 
        class="chart-btn {{chartType === 'bar' ? 'active' : ''}}"
        bindtap="switchChart"
        data-type="bar"
      >
        📊 柱状图
      </button>
      <button 
        class="chart-btn {{chartType === 'pie' ? 'active' : ''}}"
        bindtap="switchChart"
        data-type="pie"
      >
        🥧 饼图
      </button>
    </view>

    <!-- 图表展示区域 -->
    <view class="chart-container">
      <view wx:if="{{statistics.recordCount === 0}}" class="empty-chart">
        <text class="empty-icon">📊</text>
        <text class="empty-text">暂无数据</text>
      </view>

      <view wx:else class="chart-content">
        <!-- 折线图 -->
        <view wx:if="{{chartType === 'line'}}" class="line-chart">
          <view class="chart-title">每日支出趋势</view>
          <view class="line-chart-display">
            <view wx:for="{{processedDateStats}}" wx:key="date" class="line-point">
              <view class="point-date">{{item.displayDate}}</view>
              <view class="point-bar" style="height: {{item.barHeight}}rpx; background: #1976D2;"></view>
              <view class="point-amount">¥{{item.formattedAmount}}</view>
            </view>
          </view>
        </view>
        
        <!-- 柱状图 -->
        <view wx:elif="{{chartType === 'bar'}}" class="bar-chart">
          <view class="chart-title">各类型支出对比</view>
          <view class="type-bars">
            <view
              wx:for="{{Object.keys(processedTypeStats)}}"
              wx:key="*this"
              class="bar-item"
            >
              <view
                wx:for="{{expenseTypes}}"
                wx:for-item="type"
                wx:key="id"
                wx:if="{{type.id == item}}"
                class="bar-info"
              >
                <view class="bar-label">{{type.icon}} {{type.name}}</view>
                <view class="bar-visual">
                  <view
                    class="bar-fill"
                    style="width: {{processedTypeStats[item].percentage}}%; background: {{type.color}}"
                  ></view>
                </view>
                <view class="bar-amount">¥{{processedTypeStats[item].formattedAmount}}</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 饼图 -->
        <view wx:elif="{{chartType === 'pie'}}" class="pie-chart">
          <view class="chart-title">支出类型占比</view>
          <view class="pie-legend">
            <view
              wx:for="{{Object.keys(processedTypeStats)}}"
              wx:key="*this"
              class="legend-item"
            >
              <view
                wx:for="{{expenseTypes}}"
                wx:for-item="type"
                wx:key="id"
                wx:if="{{type.id == item}}"
                class="legend-info"
              >
                <view class="legend-color" style="background: {{type.color}}"></view>
                <view class="legend-text">
                  {{type.icon}} {{type.name}}
                  <text class="legend-percent">{{processedTypeStats[item].percentage}}%</text>
                </view>
                <view class="legend-amount">¥{{processedTypeStats[item].formattedAmount}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 详细记录 -->
  <view class="card" wx:if="{{statistics.records.length > 0}}">
    <view class="section-title">
      <text>详细记录</text>
      <button class="view-all-btn" bindtap="viewDetails">查看全部</button>
    </view>
    
    <view class="records-preview">
      <view 
        wx:for="{{statistics.records.slice(0, 5)}}" 
        wx:key="id" 
        class="record-item"
      >
        <view class="record-icon" style="color: {{item.typeColor}}">{{item.typeIcon}}</view>
        <view class="record-info">
          <view class="record-name">{{item.name}}</view>
          <view class="record-meta">
            <text class="record-type">{{item.typeName}}</text>
            <text class="record-date"> · {{item.date}}</text>
          </view>
        </view>
        <view class="record-amount">-¥{{item.amount}}</view>
      </view>
    </view>
  </view>
</view>

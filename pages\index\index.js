// pages/index/index.js
const storageManager = require('../../utils/storage.js')
const testData = require('../../utils/testData.js')

Page({
  data: {
    records: [],
    todayAmount: 0,
    monthAmount: 0,
    expenseTypes: []
  },

  onLoad() {
    this.setData({
      expenseTypes: getApp().globalData.expenseTypes
    })
  },

  onShow() {
    this.loadData()
  },

  // 加载数据
  loadData() {
    const records = storageManager.getAllRecords()
    
    // 计算今日支出
    const today = storageManager.formatDate(new Date())
    const todayRecords = records.filter(record => record.date === today)
    const todayAmount = todayRecords.reduce((sum, record) => sum + record.amount, 0)
    
    // 计算本月支出
    const monthRange = storageManager.getDateRange('thisMonth')
    const monthRecords = storageManager.getRecordsByDateRange(monthRange.start, monthRange.end)
    const monthAmount = monthRecords.reduce((sum, record) => sum + record.amount, 0)
    
    // 为记录添加类型信息
    const recordsWithType = records.map(record => {
      const type = this.data.expenseTypes.find(t => t.id === record.typeId)
      return {
        ...record,
        typeName: type ? type.name : '未知',
        typeIcon: type ? type.icon : '💰',
        typeColor: type ? type.color : '#999'
      }
    })

    this.setData({
      records: recordsWithType,
      todayAmount: todayAmount.toFixed(2),
      monthAmount: monthAmount.toFixed(2)
    })
  },

  // 删除记录
  deleteRecord(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      success: (res) => {
        if (res.confirm) {
          const success = storageManager.deleteRecord(id)
          if (success) {
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            this.loadData()
          } else {
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 跳转到记账页面
  goToAdd() {
    wx.switchTab({
      url: '/pages/add/add'
    })
  },

  // 跳转到统计页面
  goToStatistics() {
    wx.switchTab({
      url: '/pages/statistics/statistics'
    })
  },

  // 开发者选项：生成测试数据
  generateTestData() {
    wx.showModal({
      title: '开发者选项',
      content: '是否生成测试数据？这将添加一些示例记录用于测试功能。',
      success: (res) => {
        if (res.confirm) {
          const count = testData.generateTestData()
          wx.showToast({
            title: `已添加${count}条测试数据`,
            icon: 'success'
          })
          this.loadData()
        }
      }
    })
  },

  // 长按标题触发开发者选项
  onTitleLongPress() {
    this.generateTestData()
  }
})

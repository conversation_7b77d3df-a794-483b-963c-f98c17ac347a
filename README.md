# 星账记账小程序

一个简洁实用的微信小程序记账应用，支持本地数据存储和备份功能。

## 功能特性

### 📝 记账功能
- 支持输入支出名称、选择类型、输入金额
- 日期选择（默认今天）
- 可选备注信息
- 9种预设支出类型（餐饮、交通、购物、娱乐、医疗、教育、住房、通讯、其他）

### 📊 统计分析
- 多时间段统计：最近7天、14天、30天、本月、近三月
- 按类型筛选统计
- 三种图表展示：折线图、柱状图、饼图
- 支出总额、记录数量、平均支出等统计信息

### 💾 数据管理
- 本地数据存储，无需网络
- 数据导出为JSON文件
- 支持数据导入恢复
- 数据清空功能

### 🔒 数据安全
- 纯本地存储，数据不上传
- 支持数据备份和恢复
- 换设备时可通过备份文件迁移数据

## 项目结构

```
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── utils/
│   └── storage.js        # 数据存储管理模块
├── pages/
│   ├── index/            # 首页（记录列表）
│   ├── add/              # 记账页面
│   ├── statistics/       # 统计页面
│   └── backup/           # 备份页面
└── images/               # 图标文件目录
```

## 使用说明

### 1. 记账
- 点击底部"记账"标签或首页"记一笔"按钮
- 填写支出信息：名称、类型、金额、日期、备注
- 点击"确认记录"保存

### 2. 查看统计
- 点击底部"统计"标签
- 选择时间段和类型筛选
- 切换不同图表类型查看数据

### 3. 数据备份
- 点击底部"备份"标签
- 点击"导出数据"生成备份文件
- 通过微信分享保存备份文件

### 4. 数据恢复
- 在备份页面点击"导入数据"
- 选择之前导出的JSON备份文件
- 确认导入（会覆盖现有数据）

## 备份方案

由于没有云服务器和云存储，推荐以下备份方法：

1. **微信文件传输助手**
   - 导出数据后发送给文件传输助手
   - 在电脑端保存备份文件

2. **发送给自己**
   - 将备份文件发送给自己的其他微信号
   - 或发送到微信群中

3. **保存到手机**
   - 将备份文件保存到手机相册或文件管理器
   - 定期整理到电脑或其他存储设备

4. **邮箱备份**
   - 将备份文件通过邮箱发送给自己
   - 在邮箱中建立专门的备份文件夹

## 开发说明

### 技术栈
- 微信小程序原生开发
- 本地存储使用 `wx.setStorageSync` 和 `wx.getStorageSync`
- 文件操作使用 `wx.getFileSystemManager`

### 数据结构
```javascript
// 记录数据结构
{
  id: "唯一标识",
  name: "支出名称",
  typeId: "类型ID",
  amount: "金额（数字）",
  date: "日期（YYYY-MM-DD）",
  note: "备注",
  createTime: "创建时间戳"
}
```

### 注意事项
1. 需要准备底部导航的图标文件（见 images/README.md）
2. 小程序本地存储有10MB限制
3. 建议定期备份数据
4. 清空小程序缓存会丢失数据，请提前备份

## 部署说明

1. 在微信开发者工具中导入项目
2. 准备必要的图标文件放入 images 目录
3. 配置小程序的 AppID
4. 上传代码并提交审核

## 更新日志

### v1.0.0
- 基础记账功能
- 统计分析功能
- 数据备份恢复
- 本地数据存储

## 许可证

本项目仅供个人学习和使用。

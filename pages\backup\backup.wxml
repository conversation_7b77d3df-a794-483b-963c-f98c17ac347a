<!--pages/backup/backup.wxml-->
<view class="container">
  <!-- 数据概览 -->
  <view class="card">
    <view class="section-title">
      <text>数据概览</text>
      <button class="help-btn" bindtap="showHelp">❓</button>
    </view>
    
    <view class="data-overview">
      <view class="overview-item">
        <view class="overview-icon">📝</view>
        <view class="overview-info">
          <view class="overview-label">记录总数</view>
          <view class="overview-value">{{recordCount}} 条</view>
        </view>
      </view>
      
      <view class="overview-item">
        <view class="overview-icon">💰</view>
        <view class="overview-info">
          <view class="overview-label">支出总额</view>
          <view class="overview-value">¥{{totalAmount}}</view>
        </view>
      </view>
      
      <view class="overview-item">
        <view class="overview-icon">⏰</view>
        <view class="overview-info">
          <view class="overview-label">最后备份</view>
          <view class="overview-value">
            {{lastBackupTime ? lastBackupTime.split('T')[0] : '从未备份'}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 备份操作 -->
  <view class="card">
    <view class="section-title">数据备份</view>
    <view class="backup-description">
      <text class="desc-text">将您的记账数据导出为JSON文件，可用于数据迁移和恢复</text>
    </view>
    
    <view class="action-buttons">
      <button class="btn-primary export-btn" bindtap="exportData">
        <text class="btn-icon">📤</text>
        <text>导出数据</text>
      </button>
      
      <view class="export-tips">
        <text class="tip-item">• 数据将保存为JSON格式文件</text>
        <text class="tip-item">• 可通过微信分享给自己保存</text>
        <text class="tip-item">• 建议定期备份重要数据</text>
      </view>
    </view>
  </view>

  <!-- 数据恢复 -->
  <view class="card">
    <view class="section-title">数据恢复</view>
    <view class="restore-description">
      <text class="desc-text">从备份文件中恢复数据，将覆盖当前所有记录</text>
    </view>
    
    <view class="action-buttons">
      <button class="btn-secondary import-btn" bindtap="importData">
        <text class="btn-icon">📥</text>
        <text>导入数据</text>
      </button>
      
      <view class="import-tips">
        <text class="tip-item">• 选择之前导出的JSON文件</text>
        <text class="tip-item">• 导入将覆盖现有数据</text>
        <text class="tip-item">• 建议先备份当前数据</text>
      </view>
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="card">
    <view class="section-title">数据管理</view>
    
    <view class="management-section">
      <view class="management-item">
        <view class="management-info">
          <view class="management-title">清空所有数据</view>
          <view class="management-desc">删除所有记账记录，此操作不可恢复</view>
        </view>
        <button class="btn-danger clear-btn" bindtap="clearAllData">
          清空
        </button>
      </view>
    </view>
    
    <view class="warning-box">
      <text class="warning-icon">⚠️</text>
      <text class="warning-text">清空数据前请务必先进行备份！</text>
    </view>
  </view>

  <!-- 备份建议 -->
  <view class="card">
    <view class="section-title">备份建议</view>
    
    <view class="suggestions">
      <view class="suggestion-item">
        <text class="suggestion-icon">📅</text>
        <text class="suggestion-text">建议每月备份一次数据</text>
      </view>
      
      <view class="suggestion-item">
        <text class="suggestion-icon">☁️</text>
        <text class="suggestion-text">将备份文件保存到云盘或发送给自己</text>
      </view>
      
      <view class="suggestion-item">
        <text class="suggestion-icon">📱</text>
        <text class="suggestion-text">换设备时通过备份文件迁移数据</text>
      </view>
      
      <view class="suggestion-item">
        <text class="suggestion-icon">🔒</text>
        <text class="suggestion-text">备份文件包含敏感信息，请妥善保管</text>
      </view>
    </view>
  </view>

  <!-- 隐藏的canvas用于备用导出方案 -->
  <canvas 
    canvas-id="backupCanvas" 
    style="width: 300px; height: 200px; position: fixed; top: -1000px; left: -1000px;"
  ></canvas>
</view>

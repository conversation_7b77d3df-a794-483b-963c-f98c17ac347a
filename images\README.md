# 图标文件说明

由于微信小程序需要实际的图标文件，请准备以下图标文件：

## 需要的图标文件（建议尺寸：81x81px）

1. **home.png** - 首页图标（未选中状态）
2. **home-active.png** - 首页图标（选中状态）
3. **add.png** - 记账图标（未选中状态）
4. **add-active.png** - 记账图标（选中状态）
5. **chart.png** - 统计图标（未选中状态）
6. **chart-active.png** - 统计图标（选中状态）
7. **backup.png** - 备份图标（未选中状态）
8. **backup-active.png** - 备份图标（选中状态）

## 图标设计建议

- 使用简洁的线条风格
- 未选中状态使用灰色 (#7A7E83)
- 选中状态使用蓝色 (#4A90E2)
- 背景透明
- 格式：PNG

## 临时解决方案

如果暂时没有图标文件，可以：
1. 使用在线图标生成器创建简单图标
2. 或者先注释掉 app.json 中的 tabBar 配置，使用纯文字导航

## 图标内容建议

- **首页**: 房子图标
- **记账**: 加号或笔记图标  
- **统计**: 柱状图或饼图图标
- **备份**: 云朵或下载图标

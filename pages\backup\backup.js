// pages/backup/backup.js
const storageManager = require('../../utils/storage.js')

Page({
  data: {
    recordCount: 0,
    totalAmount: 0,
    lastBackupTime: null,
    backupData: null
  },

  onLoad() {
    this.loadBackupInfo()
  },

  onShow() {
    this.loadBackupInfo()
  },

  // 加载备份信息
  loadBackupInfo() {
    const records = storageManager.getAllRecords()
    const totalAmount = records.reduce((sum, record) => sum + record.amount, 0)
    
    // 获取上次备份时间
    const lastBackupTime = wx.getStorageSync('last_backup_time')
    
    this.setData({
      recordCount: records.length,
      totalAmount: totalAmount.toFixed(2),
      lastBackupTime: lastBackupTime || null
    })
  },

  // 导出数据
  exportData() {
    try {
      wx.showLoading({
        title: '正在导出...'
      })

      const exportData = storageManager.exportData()
      const dataStr = JSON.stringify(exportData, null, 2)
      
      // 生成文件名
      const now = new Date()
      const fileName = `星账记账备份_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}.json`
      
      // 保存到临时文件
      const fs = wx.getFileSystemManager()
      const tempFilePath = `${wx.env.USER_DATA_PATH}/${fileName}`
      
      fs.writeFile({
        filePath: tempFilePath,
        data: dataStr,
        encoding: 'utf8',
        success: () => {
          wx.hideLoading()
          
          // 分享文件
          wx.shareFileMessage({
            filePath: tempFilePath,
            fileName: fileName,
            success: () => {
              // 记录备份时间
              wx.setStorageSync('last_backup_time', new Date().toISOString())
              this.loadBackupInfo()
              
              wx.showToast({
                title: '导出成功',
                icon: 'success'
              })
            },
            fail: (err) => {
              console.error('分享失败:', err)
              // 如果分享失败，尝试保存到相册（作为文本）
              this.saveToAlbum(dataStr, fileName)
            }
          })
        },
        fail: (err) => {
          wx.hideLoading()
          console.error('写入文件失败:', err)
          wx.showToast({
            title: '导出失败',
            icon: 'error'
          })
        }
      })
    } catch (error) {
      wx.hideLoading()
      console.error('导出数据失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      })
    }
  },

  // 保存到相册（备用方案）
  saveToAlbum(dataStr, fileName) {
    // 创建canvas绘制文本
    const ctx = wx.createCanvasContext('backupCanvas', this)
    ctx.setFillStyle('#ffffff')
    ctx.fillRect(0, 0, 300, 200)
    ctx.setFillStyle('#000000')
    ctx.setFontSize(12)
    ctx.fillText('备份数据已复制到剪贴板', 10, 30)
    ctx.fillText('请手动保存到文件', 10, 50)
    ctx.fillText(`文件名: ${fileName}`, 10, 70)
    ctx.draw(false, () => {
      // 复制数据到剪贴板
      wx.setClipboardData({
        data: dataStr,
        success: () => {
          wx.showModal({
            title: '导出成功',
            content: '备份数据已复制到剪贴板，请手动保存到文件中',
            showCancel: false
          })
        }
      })
    })
  },

  // 导入数据
  importData() {
    wx.showModal({
      title: '导入数据',
      content: '导入数据将覆盖当前所有记录，是否继续？',
      success: (res) => {
        if (res.confirm) {
          this.chooseImportFile()
        }
      }
    })
  },

  // 选择导入文件
  chooseImportFile() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['json'],
      success: (res) => {
        const file = res.tempFiles[0]
        this.readImportFile(file.path)
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        // 如果选择文件失败，提供手动输入选项
        this.showManualImport()
      }
    })
  },

  // 读取导入文件
  readImportFile(filePath) {
    wx.showLoading({
      title: '正在导入...'
    })

    const fs = wx.getFileSystemManager()
    fs.readFile({
      filePath: filePath,
      encoding: 'utf8',
      success: (res) => {
        try {
          const importData = JSON.parse(res.data)
          this.processImportData(importData)
        } catch (error) {
          wx.hideLoading()
          wx.showToast({
            title: '文件格式错误',
            icon: 'error'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('读取文件失败:', err)
        wx.showToast({
          title: '读取文件失败',
          icon: 'error'
        })
      }
    })
  },

  // 处理导入数据
  processImportData(importData) {
    try {
      storageManager.importData(importData)
      wx.hideLoading()
      
      wx.showToast({
        title: '导入成功',
        icon: 'success'
      })
      
      this.loadBackupInfo()
      
      // 刷新其他页面数据
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
      
    } catch (error) {
      wx.hideLoading()
      console.error('导入数据失败:', error)
      wx.showToast({
        title: '导入失败',
        icon: 'error'
      })
    }
  },

  // 显示手动导入界面
  showManualImport() {
    wx.showModal({
      title: '手动导入',
      content: '请将备份数据粘贴到剪贴板，然后点击确定',
      success: (res) => {
        if (res.confirm) {
          wx.getClipboardData({
            success: (clipRes) => {
              try {
                const importData = JSON.parse(clipRes.data)
                this.processImportData(importData)
              } catch (error) {
                wx.showToast({
                  title: '剪贴板数据格式错误',
                  icon: 'error'
                })
              }
            },
            fail: () => {
              wx.showToast({
                title: '读取剪贴板失败',
                icon: 'error'
              })
            }
          })
        }
      }
    })
  },

  // 清空所有数据
  clearAllData() {
    wx.showModal({
      title: '危险操作',
      content: '确定要清空所有记账数据吗？此操作不可恢复！',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '最后确认',
            content: '请再次确认要删除所有数据，建议先进行备份',
            confirmColor: '#ff4757',
            success: (res2) => {
              if (res2.confirm) {
                const success = storageManager.clearAllData()
                if (success) {
                  wx.showToast({
                    title: '清空成功',
                    icon: 'success'
                  })
                  this.loadBackupInfo()
                } else {
                  wx.showToast({
                    title: '清空失败',
                    icon: 'error'
                  })
                }
              }
            }
          })
        }
      }
    })
  },

  // 查看使用说明
  showHelp() {
    wx.showModal({
      title: '备份说明',
      content: '1. 导出：将数据保存为JSON文件\n2. 导入：从JSON文件恢复数据\n3. 建议定期备份数据\n4. 换设备时可通过备份文件迁移数据',
      showCancel: false
    })
  }
})

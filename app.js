// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    try {
      const logs = wx.getStorageSync('logs') || []
      logs.unshift(Date.now())
      wx.setStorageSync('logs', logs)
    } catch (error) {
      console.warn('存储日志失败:', error)
    }

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      },
      fail: err => {
        console.warn('登录失败:', err)
      }
    })
  },
  globalData: {
    userInfo: null,
    // 支出类型配置
    expenseTypes: [
      { id: 1, name: '餐饮', icon: '🍽️', color: '#FF6B6B' },
      { id: 2, name: '交通', icon: '🚗', color: '#4ECDC4' },
      { id: 3, name: '购物', icon: '🛍️', color: '#45B7D1' },
      { id: 4, name: '娱乐', icon: '🎮', color: '#96CEB4' },
      { id: 5, name: '医疗', icon: '🏥', color: '#FFEAA7' },
      { id: 6, name: '教育', icon: '📚', color: '#DDA0DD' },
      { id: 7, name: '住房', icon: '🏠', color: '#98D8C8' },
      { id: 8, name: '通讯', icon: '📱', color: '#F7DC6F' },
      { id: 9, name: '其他', icon: '💰', color: '#BB8FCE' }
    ]
  }
})

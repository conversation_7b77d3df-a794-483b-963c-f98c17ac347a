<!--pages/index/index.wxml-->
<view class="container">
  <!-- 统计卡片 -->
  <view class="stat-cards">
    <view class="stat-card">
      <view class="stat-amount">¥{{todayAmount}}</view>
      <view class="stat-label">今日支出</view>
    </view>
    
    <view class="stat-card" style="background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%);">
      <view class="stat-amount">¥{{monthAmount}}</view>
      <view class="stat-label">本月支出</view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <button class="btn-primary" bindtap="goToAdd">
      <text class="icon">+</text>
      <text>记一笔</text>
    </button>
    <button class="btn-secondary" bindtap="goToStatistics">
      <text class="icon">📊</text>
      <text>查看统计</text>
    </button>
  </view>

  <!-- 最近记录 -->
  <view class="card">
    <view class="section-title" bindlongpress="onTitleLongPress">最近记录</view>
    
    <view wx:if="{{records.length === 0}}" class="empty-state">
      <text class="empty-icon">📝</text>
      <text class="empty-text">还没有记录，点击上方按钮开始记账吧</text>
    </view>
    
    <view wx:else class="records-list">
      <view wx:for="{{records}}" wx:key="id" class="record-item">
        <view class="record-icon" style="color: {{item.typeColor}}">{{item.typeIcon}}</view>
        
        <view class="record-info">
          <view class="record-name">{{item.name}}</view>
          <view class="record-meta">
            <text class="record-type">{{item.typeName}}</text>
            <text wx:if="{{item.note}}" class="record-note"> · {{item.note}}</text>
          </view>
          <view class="record-date">{{item.date}}</view>
        </view>
        
        <view class="record-right">
          <view class="record-amount">-¥{{item.amount}}</view>
          <button class="delete-btn" bindtap="deleteRecord" data-id="{{item.id}}">删除</button>
        </view>
      </view>
    </view>
  </view>
</view>

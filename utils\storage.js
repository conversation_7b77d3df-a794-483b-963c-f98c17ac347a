// utils/storage.js - 数据存储管理模块

const { safeStorage, errorHandler, validator } = require('./debug.js')

const STORAGE_KEYS = {
  RECORDS: 'expense_records',
  SETTINGS: 'app_settings'
}

class StorageManager {
  constructor() {
    this.initStorage()
  }

  // 初始化存储
  initStorage() {
    try {
      const records = safeStorage.getSync(STORAGE_KEYS.RECORDS, [])
      if (!records || records.length === 0) {
        safeStorage.setSync(STORAGE_KEYS.RECORDS, [])
      }

      const settings = safeStorage.getSync(STORAGE_KEYS.SETTINGS, null)
      if (!settings) {
        safeStorage.setSync(STORAGE_KEYS.SETTINGS, {
          currency: '¥',
          dateFormat: 'YYYY-MM-DD'
        })
      }
    } catch (error) {
      errorHandler.handleStorageError(error, 'init', 'all')
      // 如果存储失败，使用内存存储作为备用
      this.memoryStorage = {
        records: [],
        settings: {
          currency: '¥',
          dateFormat: 'YYYY-MM-DD'
        }
      }
    }
  }

  // 添加记录
  addRecord(record) {
    try {
      // 验证记录数据
      const validation = validator.validateRecord(record)
      if (!validation.isValid) {
        throw new Error('数据验证失败: ' + validation.errors.join(', '))
      }

      const records = this.getAllRecords()
      const newRecord = {
        id: this.generateId(),
        name: record.name.trim(),
        typeId: parseInt(record.typeId),
        amount: parseFloat(record.amount),
        date: record.date || this.formatDate(new Date()),
        note: record.note ? record.note.trim() : '',
        createTime: new Date().getTime()
      }

      records.unshift(newRecord) // 新记录添加到开头

      const success = safeStorage.setSync(STORAGE_KEYS.RECORDS, records)
      if (!success && this.memoryStorage) {
        // 如果存储失败，保存到内存
        this.memoryStorage.records = records
        console.warn('存储到本地失败，已保存到内存')
      }

      return newRecord
    } catch (error) {
      errorHandler.handleError(error, 'addRecord')
      throw error
    }
  }

  // 获取所有记录
  getAllRecords() {
    try {
      const records = safeStorage.getSync(STORAGE_KEYS.RECORDS, [])
      return Array.isArray(records) ? records : []
    } catch (error) {
      errorHandler.handleStorageError(error, 'get', STORAGE_KEYS.RECORDS)
      // 如果存储失败，返回内存存储的数据
      return this.memoryStorage ? this.memoryStorage.records : []
    }
  }

  // 根据ID获取记录
  getRecordById(id) {
    const records = this.getAllRecords()
    return records.find(record => record.id === id)
  }

  // 更新记录
  updateRecord(id, updates) {
    try {
      const records = this.getAllRecords()
      const index = records.findIndex(record => record.id === id)
      
      if (index !== -1) {
        records[index] = { ...records[index], ...updates }
        wx.setStorageSync(STORAGE_KEYS.RECORDS, records)
        return records[index]
      }
      return null
    } catch (error) {
      console.error('更新记录失败:', error)
      throw error
    }
  }

  // 删除记录
  deleteRecord(id) {
    try {
      const records = this.getAllRecords()
      const filteredRecords = records.filter(record => record.id !== id)
      wx.setStorageSync(STORAGE_KEYS.RECORDS, filteredRecords)
      return true
    } catch (error) {
      console.error('删除记录失败:', error)
      return false
    }
  }

  // 按日期范围获取记录
  getRecordsByDateRange(startDate, endDate) {
    const records = this.getAllRecords()
    return records.filter(record => {
      const recordDate = new Date(record.date)
      return recordDate >= startDate && recordDate <= endDate
    })
  }

  // 按类型获取记录
  getRecordsByType(typeId) {
    const records = this.getAllRecords()
    return records.filter(record => record.typeId === typeId)
  }

  // 获取统计数据
  getStatistics(startDate, endDate, typeId = null) {
    let records = this.getRecordsByDateRange(startDate, endDate)
    
    if (typeId) {
      records = records.filter(record => record.typeId === typeId)
    }

    const totalAmount = records.reduce((sum, record) => sum + record.amount, 0)
    const recordCount = records.length
    
    // 按类型统计
    const typeStats = {}
    records.forEach(record => {
      if (!typeStats[record.typeId]) {
        typeStats[record.typeId] = {
          count: 0,
          amount: 0
        }
      }
      typeStats[record.typeId].count++
      typeStats[record.typeId].amount += record.amount
    })

    // 按日期统计
    const dateStats = {}
    records.forEach(record => {
      const date = record.date
      if (!dateStats[date]) {
        dateStats[date] = {
          count: 0,
          amount: 0
        }
      }
      dateStats[date].count++
      dateStats[date].amount += record.amount
    })

    return {
      totalAmount,
      recordCount,
      typeStats,
      dateStats,
      records
    }
  }

  // 导出数据
  exportData() {
    try {
      const records = this.getAllRecords()
      const settings = wx.getStorageSync(STORAGE_KEYS.SETTINGS)
      
      return {
        version: '1.0',
        exportTime: new Date().toISOString(),
        records,
        settings
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      throw error
    }
  }

  // 导入数据
  importData(data) {
    try {
      if (!data.records || !Array.isArray(data.records)) {
        throw new Error('数据格式不正确')
      }

      // 备份当前数据
      const currentRecords = this.getAllRecords()
      wx.setStorageSync('backup_records_' + Date.now(), currentRecords)

      // 导入新数据
      wx.setStorageSync(STORAGE_KEYS.RECORDS, data.records)
      
      if (data.settings) {
        wx.setStorageSync(STORAGE_KEYS.SETTINGS, data.settings)
      }

      return true
    } catch (error) {
      console.error('导入数据失败:', error)
      throw error
    }
  }

  // 清空所有数据
  clearAllData() {
    try {
      wx.removeStorageSync(STORAGE_KEYS.RECORDS)
      wx.removeStorageSync(STORAGE_KEYS.SETTINGS)
      this.initStorage()
      return true
    } catch (error) {
      console.error('清空数据失败:', error)
      return false
    }
  }

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 获取日期范围
  getDateRange(type) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    switch (type) {
      case '7days':
        return {
          start: new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000),
          end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        }
      case '14days':
        return {
          start: new Date(today.getTime() - 13 * 24 * 60 * 60 * 1000),
          end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        }
      case '30days':
        return {
          start: new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000),
          end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        }
      case 'thisMonth':
        return {
          start: new Date(now.getFullYear(), now.getMonth(), 1),
          end: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
        }
      case '3months':
        return {
          start: new Date(now.getFullYear(), now.getMonth() - 2, 1),
          end: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
        }
      default:
        return {
          start: today,
          end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
        }
    }
  }
}

// 创建全局实例
const storageManager = new StorageManager()

module.exports = storageManager

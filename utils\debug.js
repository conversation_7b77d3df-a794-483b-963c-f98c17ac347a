// utils/debug.js - 调试和错误处理工具

// 安全的存储操作
const safeStorage = {
  // 安全获取存储数据
  getSync(key, defaultValue = null) {
    try {
      const value = wx.getStorageSync(key)
      return value !== '' ? value : defaultValue
    } catch (error) {
      console.warn(`获取存储数据失败 [${key}]:`, error)
      return defaultValue
    }
  },

  // 安全设置存储数据
  setSync(key, value) {
    try {
      wx.setStorageSync(key, value)
      return true
    } catch (error) {
      console.warn(`设置存储数据失败 [${key}]:`, error)
      return false
    }
  },

  // 安全删除存储数据
  removeSync(key) {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (error) {
      console.warn(`删除存储数据失败 [${key}]:`, error)
      return false
    }
  }
}

// 调试信息收集
const debugInfo = {
  // 获取系统信息
  getSystemInfo() {
    try {
      return wx.getSystemInfoSync()
    } catch (error) {
      console.warn('获取系统信息失败:', error)
      return {}
    }
  },

  // 获取存储信息
  getStorageInfo() {
    try {
      return wx.getStorageInfoSync()
    } catch (error) {
      console.warn('获取存储信息失败:', error)
      return { keys: [], currentSize: 0, limitSize: 0 }
    }
  },

  // 生成调试报告
  generateReport() {
    const systemInfo = this.getSystemInfo()
    const storageInfo = this.getStorageInfo()
    
    return {
      timestamp: new Date().toISOString(),
      system: {
        platform: systemInfo.platform || 'unknown',
        version: systemInfo.version || 'unknown',
        SDKVersion: systemInfo.SDKVersion || 'unknown'
      },
      storage: {
        keys: storageInfo.keys || [],
        currentSize: storageInfo.currentSize || 0,
        limitSize: storageInfo.limitSize || 0,
        usage: storageInfo.limitSize > 0 
          ? ((storageInfo.currentSize / storageInfo.limitSize) * 100).toFixed(2) + '%'
          : '0%'
      },
      app: {
        version: '1.0.0',
        buildTime: new Date().toISOString()
      }
    }
  }
}

// 错误处理
const errorHandler = {
  // 处理存储错误
  handleStorageError(error, operation, key) {
    console.error(`存储操作失败 [${operation}] [${key}]:`, error)
    
    // 显示用户友好的错误信息
    if (error.message && error.message.includes('INVALID_LOGIN')) {
      wx.showToast({
        title: '存储服务暂时不可用',
        icon: 'none',
        duration: 2000
      })
    } else {
      wx.showToast({
        title: '数据操作失败',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 处理网络错误
  handleNetworkError(error, operation) {
    console.error(`网络操作失败 [${operation}]:`, error)
    wx.showToast({
      title: '网络连接失败',
      icon: 'none',
      duration: 2000
    })
  },

  // 通用错误处理
  handleError(error, context = '') {
    console.error(`错误 [${context}]:`, error)
    
    // 根据错误类型显示不同的提示
    let message = '操作失败'
    if (error.message) {
      if (error.message.includes('storage')) {
        message = '数据存储失败'
      } else if (error.message.includes('network')) {
        message = '网络连接失败'
      } else if (error.message.includes('permission')) {
        message = '权限不足'
      }
    }
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }
}

// 性能监控
const performance = {
  timers: {},
  
  // 开始计时
  start(name) {
    this.timers[name] = Date.now()
  },
  
  // 结束计时并输出
  end(name) {
    if (this.timers[name]) {
      const duration = Date.now() - this.timers[name]
      console.log(`性能监控 [${name}]: ${duration}ms`)
      delete this.timers[name]
      return duration
    }
    return 0
  }
}

// 数据验证
const validator = {
  // 验证记录数据
  validateRecord(record) {
    const errors = []
    
    if (!record.name || typeof record.name !== 'string' || record.name.trim() === '') {
      errors.push('名称不能为空')
    }
    
    if (!record.typeId || typeof record.typeId !== 'number') {
      errors.push('类型必须选择')
    }
    
    if (!record.amount || isNaN(parseFloat(record.amount)) || parseFloat(record.amount) <= 0) {
      errors.push('金额必须大于0')
    }
    
    if (!record.date || !/^\d{4}-\d{2}-\d{2}$/.test(record.date)) {
      errors.push('日期格式不正确')
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    }
  },
  
  // 验证导入数据
  validateImportData(data) {
    const errors = []
    
    if (!data || typeof data !== 'object') {
      errors.push('数据格式不正确')
      return { isValid: false, errors }
    }
    
    if (!data.records || !Array.isArray(data.records)) {
      errors.push('记录数据不存在或格式不正确')
    }
    
    if (!data.version) {
      errors.push('缺少版本信息')
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    }
  }
}

module.exports = {
  safeStorage,
  debugInfo,
  errorHandler,
  performance,
  validator
}

/* pages/index/index.wxss */
.container {
  padding: 20rpx 24rpx;
  min-height: 100vh;
  background: #f5f5f5;
  width: 100%;
  box-sizing: border-box;
}

/* 统计卡片 */
.stat-cards {
  display: flex;
  gap: 16rpx;
  margin-bottom: 30rpx;
  width: 100%;
}

.stat-card {
  flex: 1;
  background: #1976D2;
  color: white;
  text-align: center;
  border-radius: 12rpx;
  padding: 32rpx 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(25, 118, 210, 0.2);
}

.stat-amount {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.stat-label {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 30rpx;
  width: 100%;
}

.quick-actions .btn-primary,
.quick-actions .btn-secondary {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx 20rpx;
  font-size: 32rpx;
}

.quick-actions .icon {
  font-size: 36rpx;
}

/* 记录列表 */
.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 20rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.6;
}

.records-list {
  max-height: 800rpx;
  overflow-y: auto;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 80rpx;
  text-align: center;
}

.record-info {
  flex: 1;
}

.record-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.record-meta {
  margin-bottom: 8rpx;
}

.record-type {
  font-size: 24rpx;
  color: #666;
}

.record-note {
  font-size: 24rpx;
  color: #999;
}

.record-date {
  font-size: 24rpx;
  color: #999;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4757;
  margin-bottom: 12rpx;
}

.delete-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

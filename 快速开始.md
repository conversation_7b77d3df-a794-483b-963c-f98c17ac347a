# 星账记账小程序 - 快速开始指南

## 🚀 立即开始

### 1. 准备工作
- 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 注册微信小程序账号（如果需要发布）

### 2. 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择当前项目目录
4. 填入 AppID（测试可以使用测试号）

### 3. 项目已完成
项目已经进行了以下优化并完成：

✅ **底部导航图标**：已添加完整的图标文件，导航更美观
✅ **存储错误已修复**：添加了完善的错误处理和备用存储方案
✅ **WXML语法已修复**：修复了所有复杂表达式导致的编译错误
✅ **调试工具已添加**：新增调试和错误处理工具
✅ **统计页面已优化**：预计算所有复杂数据，提升性能和稳定性

#### 图标文件已完整
`images` 目录包含所有必要的图标文件：
- ✅ home.png / home-active.png - 首页图标
- ✅ add.png / add-active.png - 记账图标
- ✅ chart.png / chart-active.png - 统计图标
- ✅ backup.png / backup-active.png - 备份图标

### 4. 开始使用

#### 测试数据生成
1. 在首页长按"最近记录"标题
2. 选择"确定"生成测试数据
3. 这将添加20条示例记录用于测试各项功能

#### 基本功能测试
1. **记账功能**：点击底部"记账"或首页"记一笔"
2. **查看统计**：点击底部"统计"查看图表和数据分析
3. **数据备份**：点击底部"备份"导出数据文件
4. **数据恢复**：在备份页面导入之前的备份文件

## 📱 功能说明

### 记账页面
- 支出名称：必填，描述这笔支出
- 支出类型：必选，9种预设类型
- 支出金额：必填，支持小数点后两位
- 支出日期：默认今天，可选择其他日期
- 备注：可选，补充说明信息

### 统计页面
- **时间筛选**：7天、14天、30天、本月、近三月
- **类型筛选**：可按支出类型筛选
- **图表展示**：折线图、柱状图、饼图三种形式
- **数据概览**：总支出、记录数、平均支出

### 备份页面
- **数据导出**：生成JSON格式备份文件
- **数据导入**：从备份文件恢复数据
- **数据清空**：删除所有记录（谨慎操作）

## 🔧 开发说明

### 项目结构
```
├── app.js              # 应用入口
├── app.json            # 应用配置
├── app.wxss            # 全局样式
├── utils/
│   ├── storage.js      # 数据存储管理
│   └── testData.js     # 测试数据生成
└── pages/
    ├── index/          # 首页
    ├── add/            # 记账页
    ├── statistics/     # 统计页
    └── backup/         # 备份页
```

### 数据存储
- 使用微信小程序本地存储API
- 数据格式：JSON
- 存储限制：10MB
- 支持导出/导入功能

### 自定义配置
可以在 `app.js` 的 `globalData.expenseTypes` 中修改支出类型：
```javascript
expenseTypes: [
  { id: 1, name: '餐饮', icon: '🍽️', color: '#FF6B6B' },
  // 添加更多类型...
]
```

## 🎯 使用建议

### 日常使用
1. 每次消费后及时记录
2. 定期查看统计分析消费习惯
3. 每月备份一次数据

### 数据安全
1. 定期导出备份文件
2. 将备份文件保存到多个位置
3. 换设备前务必备份数据

### 备份方法
1. **微信文件传输助手**：发送给自己保存
2. **邮箱备份**：发送到邮箱保存
3. **云盘存储**：上传到百度网盘等
4. **本地保存**：保存到电脑或U盘

## ❓ 常见问题

**Q: 清空微信小程序后数据会丢失吗？**
A: 是的，清空小程序缓存会丢失所有数据，请提前备份。

**Q: 可以在多个设备间同步数据吗？**
A: 通过备份和恢复功能可以在设备间迁移数据。

**Q: 支持收入记录吗？**
A: 当前版本只支持支出记录，可以根据需要扩展功能。

**Q: 数据存储有限制吗？**
A: 微信小程序本地存储限制为10MB，足够存储大量记录。

## 📞 技术支持

如果遇到问题或需要功能扩展，可以：
1. 查看项目README.md文档
2. 检查微信开发者工具控制台错误信息
3. 确保按照快速开始指南正确配置项目

---

**开始记账，管理财务，从现在开始！** 💰📊

// utils/testData.js - 测试数据生成器

const storageManager = require('./storage.js')

// 生成测试数据
function generateTestData() {
  const testRecords = [
    // 最近7天的数据
    { name: '午餐', typeId: 1, amount: 25.5, date: getDateString(-1), note: '公司楼下' },
    { name: '地铁', typeId: 2, amount: 4.0, date: getDateString(-1), note: '上班通勤' },
    { name: '咖啡', typeId: 1, amount: 18.0, date: getDateString(-2), note: '星巴克' },
    { name: '打车', typeId: 2, amount: 32.5, date: getDateString(-2), note: '回家' },
    { name: '晚餐', typeId: 1, amount: 45.0, date: getDateString(-3), note: '和朋友聚餐' },
    { name: '电影票', typeId: 4, amount: 55.0, date: getDateString(-3), note: '复仇者联盟' },
    { name: '买衣服', typeId: 3, amount: 299.0, date: getDateString(-4), note: 'UNIQLO' },
    { name: '早餐', typeId: 1, amount: 12.0, date: getDateString(-4), note: '豆浆油条' },
    { name: '公交', typeId: 2, amount: 2.0, date: getDateString(-5), note: '上班' },
    { name: '午餐', typeId: 1, amount: 28.0, date: getDateString(-5), note: '麻辣烫' },
    { name: '买书', typeId: 6, amount: 89.0, date: getDateString(-6), note: 'JavaScript高级程序设计' },
    { name: '晚餐', typeId: 1, amount: 35.0, date: getDateString(-6), note: '家常菜' },
    
    // 更早的数据
    { name: '手机充值', typeId: 8, amount: 50.0, date: getDateString(-10), note: '话费充值' },
    { name: '超市购物', typeId: 3, amount: 156.8, date: getDateString(-12), note: '日用品' },
    { name: '看病', typeId: 5, amount: 120.0, date: getDateString(-15), note: '感冒' },
    { name: '房租', typeId: 7, amount: 2500.0, date: getDateString(-20), note: '月租' },
    { name: '聚餐', typeId: 1, amount: 180.0, date: getDateString(-25), note: '同学聚会' },
    { name: '买鞋', typeId: 3, amount: 399.0, date: getDateString(-30), note: 'Nike运动鞋' },
    { name: '培训费', typeId: 6, amount: 1200.0, date: getDateString(-35), note: '编程课程' },
    { name: '其他支出', typeId: 9, amount: 66.6, date: getDateString(-40), note: '杂项' }
  ]

  // 添加测试数据
  testRecords.forEach(record => {
    try {
      storageManager.addRecord(record)
    } catch (error) {
      console.error('添加测试数据失败:', error)
    }
  })

  return testRecords.length
}

// 获取指定天数前的日期字符串
function getDateString(daysAgo) {
  const date = new Date()
  date.setDate(date.getDate() + daysAgo)
  return storageManager.formatDate(date)
}

// 清空测试数据
function clearTestData() {
  return storageManager.clearAllData()
}

// 生成随机测试数据
function generateRandomTestData(count = 50) {
  const names = [
    '早餐', '午餐', '晚餐', '咖啡', '奶茶', '零食', '水果',
    '地铁', '公交', '打车', '加油', '停车费',
    '衣服', '鞋子', '包包', '化妆品', '日用品', '超市购物',
    '电影', '游戏', 'KTV', '旅游', '运动',
    '看病', '买药', '体检', '牙科',
    '培训', '书籍', '课程', '考试费',
    '房租', '水电费', '物业费', '网费',
    '话费', '流量', '宽带',
    '礼品', '红包', '其他'
  ]

  const typeIds = [1, 2, 3, 4, 5, 6, 7, 8, 9]
  const records = []

  for (let i = 0; i < count; i++) {
    const daysAgo = Math.floor(Math.random() * 90) // 最近90天
    const record = {
      name: names[Math.floor(Math.random() * names.length)],
      typeId: typeIds[Math.floor(Math.random() * typeIds.length)],
      amount: parseFloat((Math.random() * 500 + 5).toFixed(2)), // 5-505元
      date: getDateString(-daysAgo),
      note: Math.random() > 0.7 ? '随机备注' : '' // 30%概率有备注
    }
    
    try {
      storageManager.addRecord(record)
      records.push(record)
    } catch (error) {
      console.error('添加随机测试数据失败:', error)
    }
  }

  return records.length
}

module.exports = {
  generateTestData,
  clearTestData,
  generateRandomTestData
}
